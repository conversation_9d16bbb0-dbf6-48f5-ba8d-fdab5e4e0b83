"use client"

import {
  Background,
  Controls,
  MarkerType,
  MiniMap,
  ReactFlow,
  ReactFlowProvider,
  addEdge,
  useEdgesState,
  useNodesState,
  type Connection,
  type Edge,
  type Node,
  type OnConnect,
} from "@xyflow/react"
import "@xyflow/react/dist/style.css"
import { useCallback, useEffect, useRef, useState } from "react"
import { CustomNode } from "./CustomNode"
import { ShoshinEdge } from "./ShoshinEdge"

const nodeTypes = {
  custom: CustomNode,
}

const edgeTypes = {
  shoshin: ShoshinEdge,
}

const initialNodes: Node[] = [
  {
    id: "start-1",
    type: "custom",
    position: { x: 250, y: 100 },
    data: { 
      label: "Start",
      type: "start",
      description: "Start Workflow"
    },
  },
]

const initialEdges: Edge[] = []

function MainCanvasInner() {
  const reactFlowWrapper = useRef<HTMLDivElement>(null)
  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes)
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges)
  const [reactFlowInstance, setReactFlowInstance] = useState<any>(null)

  // Auto layout function
  const applyAutoLayout = useCallback(() => {
    if (nodes.length === 0) return

    // Create adjacency list to understand node relationships
    const adjacencyList = new Map<string, string[]>()
    const inDegree = new Map<string, number>()

    // Initialize
    nodes.forEach(node => {
      adjacencyList.set(node.id, [])
      inDegree.set(node.id, 0)
    })

    // Build graph
    edges.forEach(edge => {
      if (edge.source && edge.target) {
        adjacencyList.get(edge.source)?.push(edge.target)
        inDegree.set(edge.target, (inDegree.get(edge.target) || 0) + 1)
      }
    })

    // Find root nodes (nodes with no incoming edges)
    const rootNodes = nodes.filter(node => inDegree.get(node.id) === 0)

    // If no root nodes, use the first node as root
    if (rootNodes.length === 0 && nodes.length > 0) {
      rootNodes.push(nodes[0])
    }

    // Layout configuration
    const HORIZONTAL_SPACING = 300
    const VERTICAL_SPACING = 150
    const START_X = 100
    const START_Y = 100

    // Track positioned nodes and their levels
    const positioned = new Set<string>()
    const levels = new Map<string, number>()
    const levelNodes = new Map<number, string[]>()

    // BFS to assign levels
    const queue: Array<{ nodeId: string; level: number }> = []

    rootNodes.forEach(node => {
      queue.push({ nodeId: node.id, level: 0 })
      levels.set(node.id, 0)
    })

    while (queue.length > 0) {
      const { nodeId, level } = queue.shift()!

      if (positioned.has(nodeId)) continue
      positioned.add(nodeId)

      // Add to level tracking
      if (!levelNodes.has(level)) {
        levelNodes.set(level, [])
      }
      levelNodes.get(level)!.push(nodeId)

      // Add children to queue
      const children = adjacencyList.get(nodeId) || []
      children.forEach(childId => {
        if (!positioned.has(childId)) {
          const childLevel = level + 1
          levels.set(childId, childLevel)
          queue.push({ nodeId: childId, level: childLevel })
        }
      })
    }

    // Position nodes based on levels
    const updatedNodes = nodes.map(node => {
      const level = levels.get(node.id) || 0
      const nodesInLevel = levelNodes.get(level) || []
      const indexInLevel = nodesInLevel.indexOf(node.id)

      // Center nodes in each level
      const totalNodesInLevel = nodesInLevel.length
      const levelWidth = (totalNodesInLevel - 1) * HORIZONTAL_SPACING
      const startXForLevel = START_X - levelWidth / 2

      return {
        ...node,
        position: {
          x: startXForLevel + indexInLevel * HORIZONTAL_SPACING,
          y: START_Y + level * VERTICAL_SPACING
        }
      }
    })

    setNodes(updatedNodes)
  }, [nodes, edges, setNodes])

  // Listen for auto layout events
  useEffect(() => {
    const handleAutoLayout = () => {
      applyAutoLayout()
    }

    window.addEventListener('trigger-auto-layout', handleAutoLayout)
    return () => {
      window.removeEventListener('trigger-auto-layout', handleAutoLayout)
    }
  }, [applyAutoLayout])

  const onConnect: OnConnect = useCallback(
    (params) => setEdges((eds) => addEdge(params, eds)),
    [setEdges]
  )

  const onDragOver = useCallback((event: React.DragEvent) => {
    event.preventDefault()
    event.dataTransfer.dropEffect = "move"
  }, [])

  const onDrop = useCallback(
    (event: React.DragEvent) => {
      event.preventDefault()

      const reactFlowBounds = reactFlowWrapper.current?.getBoundingClientRect()
      const data = event.dataTransfer.getData("application/reactflow")

      if (typeof data === "undefined" || !data || !reactFlowBounds) {
        return
      }

      const blockData = JSON.parse(data)
      const position = reactFlowInstance?.screenToFlowPosition({
        x: event.clientX - reactFlowBounds.left,
        y: event.clientY - reactFlowBounds.top,
      })

      const newNode: Node = {
        id: `${blockData.type}-${Date.now()}`,
        type: "custom",
        position,
        data: {
          label: blockData.name,
          type: blockData.type,
          description: blockData.description,
        },
      }

      setNodes((nds) => nds.concat(newNode))
    },
    [reactFlowInstance, setNodes]
  )

  return (
    <div className="w-full h-full" ref={reactFlowWrapper}>
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onConnect={onConnect}
        onInit={setReactFlowInstance}
        onDrop={onDrop}
        onDragOver={onDragOver}
        nodeTypes={nodeTypes}
        edgeTypes={edgeTypes}
        fitView
        className="bg-gray-900"
        defaultEdgeOptions={{
          type: 'shoshin',
          animated: true,
        }}
        connectionLineStyle={{
          stroke: '#6366f1',
          strokeWidth: 2,
          strokeDasharray: '5,5'
        }}
        snapToGrid={true}
        snapGrid={[15, 15]}
      >
        <Background
          color="#374151"
          gap={20}
          size={1}
        />
        <Controls
          className="bg-gray-800 border-gray-600 [&>button]:bg-gray-700 [&>button]:border-gray-600 [&>button]:text-gray-300 [&>button:hover]:bg-gray-600"
        />
        <MiniMap
          className="bg-gray-800 border-gray-600"
          nodeColor="#6366f1"
          maskColor="rgba(0, 0, 0, 0.3)"
          pannable
          zoomable
        />
      </ReactFlow>
    </div>
  )
}

export function MainCanvas() {
  return (
    <ReactFlowProvider>
      <MainCanvasInner />
    </ReactFlowProvider>
  )
}
