"use client"

import { BaseEdge, type EdgeProps, getSmoothStepPath, MarkerType } from "@xyflow/react"

export const ShoshinEdge = ({
  id,
  sourceX,
  sourceY,
  targetX,
  targetY,
  sourcePosition,
  targetPosition,
  style = {},
  markerEnd,
}: EdgeProps) => {
  const isHorizontal = sourcePosition === 'right' || sourcePosition === 'left'

  const [edgePath] = getSmoothStepPath({
    sourceX,
    sourceY,
    sourcePosition,
    targetX,
    targetY,
    targetPosition,
    borderRadius: 8,
    offset: isHorizontal ? 30 : 20,
  })

  return (
    <>
      <defs>
        <marker
          id={`shoshin-arrow-${id}`}
          viewBox="0 0 10 10"
          refX="9"
          refY="3"
          markerWidth="6"
          markerHeight="6"
          orient="auto"
          markerUnits="strokeWidth"
        >
          <path
            d="M0,0 L0,6 L9,3 z"
            fill="#6366f1"
            stroke="#6366f1"
            strokeWidth="1"
          />
        </marker>
      </defs>
      <BaseEdge
        path={edgePath}
        style={{
          strokeWidth: 2,
          stroke: '#6366f1',
          strokeDasharray: '5,5',
          ...style,
        }}
        markerEnd={`url(#shoshin-arrow-${id})`}
      />
    </>
  )
}
